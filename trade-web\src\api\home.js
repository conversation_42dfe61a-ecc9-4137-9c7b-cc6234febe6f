import service from './request'

/**
 * 获取首页轮播图
 * @returns {Promise}
 */
export function getBanners() {
  return service({
    url: '/v1/public/home/<USER>',
    method: 'get'
  })
}

/**
 * 获取首页分类树
 * @returns {Promise}
 */
export function getCategories() {
  return service({
    url: '/v1/public/home/<USER>',
    method: 'get'
  })
}

/**
 * 获取企业风采展示
 * @param {object} params - 查询参数, e.g., { page: 1, page_size: 6 }
 * @returns {Promise}
 */
export function getMerchantShowcase(params) {
  return service({
    url: '/v1/public/home/<USER>/showcase',
    method: 'get',
    params
  })
}

/**
 * 获取首页热门分类
 * @param {object} params - 查询参数, e.g., { page: 1, page_size: 10 }
 * @returns {Promise}
 */
export function getHotCategories(params) {
  return service({
    url: '/v1/public/home/<USER>/category',
    method: 'get',
    params
  })
} 