<template>
  <div class="category-detail">
    <!-- 固定头部 -->
    <HeaderNav />
    
    <!-- 主要内容区域 -->
    <main class="main-content">
      <div class="container">
        <!-- 顶部面包屑和标题区域 -->
        <div class="page-header">
          <!-- 面包屑导航 -->
          <!-- <div class="breadcrumb">
            <span @click="$router.push('/')" class="breadcrumb-item">
              {{ currentLang === 'zh' ? '首页' : 'Home' }}
            </span>
            <span class="separator">/</span>
            <span v-if="currentCategory" class="breadcrumb-item active">
              {{ currentLang === 'zh' ? currentCategory.name : currentCategory.nameEn }}
            </span>
          </div> -->
          
          <!-- 分类标题和统计 -->
          <div class="section-header">
            <h1 class="section-title">
              {{ currentCategory ? (currentLang === 'zh' ? currentCategory.name : currentCategory.nameEn) : (currentLang === 'zh' ? '产品列表' : 'Product List') }}
            </h1>
            <div class="product-count">
              {{ currentLang === 'zh' ? '共找到' : 'Found' }} {{ filteredProducts.length }} {{ currentLang === 'zh' ? '个产品' : 'products' }}
            </div>
          </div>
        </div>
        
        <!-- 产品列表 -->
        <section class="products-section">
            <!-- 产品列表 -->
            <div class="products-list">
              <div v-if="loading" class="loading">
                <el-icon class="is-loading"><Loading /></el-icon>
                {{ currentLang === 'zh' ? '加载中...' : 'Loading...' }}
              </div>
              
              <div v-else-if="filteredProducts.length === 0" class="empty-state">
                <el-icon><Box /></el-icon>
                <p>{{ currentLang === 'zh' ? '暂无产品' : 'No products found' }}</p>
              </div>
              
              <div v-else>
                <ProductCard 
                  v-for="product in paginatedProducts" 
                  :key="product.id"
                  :product="product"
                  :company="getCompanyById(product.companyId)"
                />
                
                <!-- 分页 -->
                <div class="pagination-wrapper">
                  <el-pagination
                    :current-page="currentPage"
                    :page-size="pageSize"
                    :total="filteredProducts.length"
                    layout="prev, pager, next"
                    @current-change="handlePageChange"
                  />
                </div>
              </div>
            </div>
        </section>
      </div>
    </main>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Loading, Box } from '@element-plus/icons-vue'
import HeaderNav from '../components/HeaderNav.vue'
import ProductCard from '../components/ProductCard.vue'
import { categoryData, detailedProducts, companies } from '../data/mockData.js'
import { useLanguageStore } from '../stores/language.js'

export default {
  name: 'CategoryDetailView',
  components: {
    HeaderNav,
    ProductCard
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const languageStore = useLanguageStore()
    
    const selectedCategoryId = ref(null)
    const currentPage = ref(1)
    const pageSize = ref(10)
    const loading = ref(false)
    
    const currentLang = computed(() => languageStore.currentLang)
    
    // 根据分类ID查找分类信息
    const findCategoryById = (categoryId) => {
      const findInCategory = (categories) => {
        for (const category of categories) {
          if (category.id === categoryId) return category
          if (category.children) {
            const found = findInCategory(category.children)
            if (found) return found
          }
        }
        return null
      }
      return findInCategory(categoryData)
    }
    
    const currentCategory = computed(() => {
      return selectedCategoryId.value ? findCategoryById(selectedCategoryId.value) : null
    })
    
    // 根据选中分类筛选产品
    const filteredProducts = computed(() => {
      if (!selectedCategoryId.value) return detailedProducts
      return detailedProducts.filter(product => product.categoryId === selectedCategoryId.value)
    })
    
    // 分页后的产品
    const paginatedProducts = computed(() => {
      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value
      return filteredProducts.value.slice(start, end)
    })
    
    // 根据公司ID获取公司信息
    const getCompanyById = (companyId) => {
      return companies.find(company => company.id === companyId)
    }
    
    // 处理分类切换
    const handleCategoryChange = (categoryId) => {
      selectedCategoryId.value = categoryId
      currentPage.value = 1
      router.push(`/category/${categoryId}`)
    }
    
    // 处理分页
    const handlePageChange = (page) => {
      currentPage.value = page
      // 滚动到顶部
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }
    
    // 初始化
    onMounted(() => {
      const categoryId = parseInt(route.params.categoryId)
      if (categoryId) {
        selectedCategoryId.value = categoryId
      }
      languageStore.initLanguage()
    })
    
    // 监听路由变化
    watch(() => route.params.categoryId, (newCategoryId) => {
      if (newCategoryId) {
        selectedCategoryId.value = parseInt(newCategoryId)
        currentPage.value = 1
      }
    })
    
    return {
      selectedCategoryId,
      currentCategory,
      filteredProducts,
      paginatedProducts,
      currentPage,
      pageSize,
      loading,
      currentLang,
      handleCategoryChange,
      handlePageChange,
      getCompanyById
    }
  }
}
</script>

<style lang="scss" scoped>
.category-detail {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #ffffff 50%, #f1f5f9 100%);
}

.main-content {
  padding-top: 140px; // 调整以适应新的头部高度 (主导航70px + 二级导航50px + 间距20px)
  padding-bottom: 48px;
  
  .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 30px;
  }
  
  .page-header {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .products-section {
    width: 100%;
  }
}

.breadcrumb {
  margin-bottom: 16px;
  font-size: 14px;
  
  .breadcrumb-item {
    color: #6b7280;
    text-decoration: none;
    cursor: pointer;
    
    &:hover {
      color: #2563eb;
    }
    
    &.active {
      color: #1f2937;
      font-weight: 500;
    }
  }
  
  .separator {
    margin: 0 8px;
    color: #d1d5db;
  }
}

.section-header {
  .section-title {
    margin: 0 0 8px 0;
    font-size: 32px;
    font-weight: 700;
    color: #1f2937;
  }
  
  .product-count {
    font-size: 16px;
    color: #6b7280;
  }
}

.products-list {
  .loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 48px;
    font-size: 16px;
    color: #6b7280;
  }
  
  .empty-state {
    text-align: center;
    padding: 48px;
    color: #6b7280;
    
    .el-icon {
      font-size: 48px;
      margin-bottom: 16px;
      color: #d1d5db;
    }
    
    p {
      font-size: 16px;
      margin: 0;
    }
  }
  
  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 32px;
    padding: 24px 0;
  }
}

// 响应式适配
@media (max-width: 1366px) and (min-width: 1025px) {
  .main-content {
    padding-top: 125px; // 主导航65px + 二级导航45px + 间距15px
    
    .container {
      max-width: 1200px;
      padding: 0 24px;
    }
  }
}

@media (max-width: 1024px) and (min-width: 969px) {
  .main-content {
    padding-top: 115px; // 主导航60px + 二级导航42px + 间距13px
    
    .container {
      max-width: 1000px;
      padding: 0 20px;
    }
  }
}

@media (max-width: 968px) {
  .main-content {
    padding-top: 180px; // 移动端头部较高
    padding-bottom: 32px;
    
    .container {
      padding: 0 20px;
      max-width: 100%;
    }
    
    .page-header {
      margin-bottom: 24px;
      padding-bottom: 20px;
    }
    
    .products-section {
      width: 100%;
    }
  }
  
  .section-header .section-title {
    font-size: 24px;
  }
}
</style> 