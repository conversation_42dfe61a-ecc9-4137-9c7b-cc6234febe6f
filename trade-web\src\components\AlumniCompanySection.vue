<template>
  <section class="alumni-company-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">
          {{ currentLang === 'zh' ? '校友企业风采' : 'Alumni Enterprises' }}
        </h2>
        <p class="section-subtitle">
          {{ currentLang === 'zh' ? '展示华南理工大学校友创办的优秀企业' : 'Showcasing outstanding enterprises founded by SCUT alumni' }}
        </p>
      </div>
      
      <div class="company-grid">
        <div 
          v-for="company in companies" 
          :key="company.id"
          class="company-card"
          @click="viewCompanyDetails(company)"
        >
          <div class="company-image">
            <img :src="getAttributeValue(company, 3) || defaultImage" :alt="getAttributeValue(company, 1) || company.name" />
          </div>
          <div class="company-info">
            <div class="company-logo">
              <img :src="getAttributeValue(company, 2) || defaultLogo" :alt="(getAttributeValue(company, 1) || company.name) + ' logo'" />
            </div>
            <h3 class="company-name">{{ getAttributeValue(company, 1) || company.name }}</h3>
            <p class="company-description">
              {{ getAttributeValue(company, 12) || '暂无简介' }}
            </p>
            <div class="company-details">
              <!-- <div class="company-rating">
                <el-rate 
                  v-model="company.rating" 
                  disabled 
                  show-score 
                  text-color="#ff9900"
                  score-template="{value}"
                />
              </div> -->
              <div class="company-established">
                <span class="label">{{ currentLang === 'zh' ? '成立年份:' : 'Established:' }}</span>
                <span class="value">{{ getAttributeValue(company, 9) || 'N/A' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="view-more">
        <el-button type="primary" plain @click="viewAllCompanies">
          {{ currentLang === 'zh' ? '查看更多企业' : 'View More Companies' }}
        </el-button>
      </div>
    </div>
  </section>
</template>

<script>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useLanguageStore } from '../stores/language.js'
import defaultLogo from '../assets/logo.png' 

export default {
  name: 'AlumniCompanySection',
  props: {
    companies: {
      type: Array,
      required: true,
      default: () => []
    }
  },
  setup(props) {
    const router = useRouter()
    const languageStore = useLanguageStore()
    const currentLang = computed(() => languageStore.currentLang)
    
    // 默认主图
    const defaultImage = 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80'
    
    // 根据 attribute_id 获取属性值的辅助函数
    const getAttributeValue = (company, attributeId) => {
      if (!company.attributes) return null
      const attribute = company.attributes.find(attr => attr.attribute_id === attributeId)
      return attribute ? attribute.value : null
    }
    
    // 为每个公司添加默认评分（因为API数据中可能没有评分）
    const companiesWithRating = computed(() => {
      return props.companies.map(company => ({
        ...company,
        rating: company.rating || 4.5 // 默认评分
      }))
    })
    
    const viewCompanyDetails = (company) => {
      router.push(`/company/${company.id}`)
    }
    
    const viewAllCompanies = () => {
      // 暂无公司列表页面，可先跳转至首页
      router.push('/')
      // 也可以改为显示提示
      // ElMessage.info(currentLang.value === 'zh' ? '功能开发中' : 'Feature in development')
    }

    console.log('AlumniCompanySection 接收到的企业数据:', props.companies)
    
    return {
      companies: companiesWithRating,
      currentLang,
      defaultLogo,
      defaultImage,
      getAttributeValue,
      viewCompanyDetails,
      viewAllCompanies
    }
  }
}
</script>

<style lang="scss" scoped>
.alumni-company-section {
  padding: 60px 0;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(241, 245, 249, 0.5) 100%);
  
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
  
  .section-header {
    text-align: center;
    margin-bottom: 40px;
    
    .section-title {
      font-size: 32px;
      font-weight: 700;
      color: #1e3a8a;
      margin: 0 0 12px 0;
    }
    
    .section-subtitle {
      font-size: 16px;
      color: #64748b;
      max-width: 600px;
      margin: 0 auto;
    }
  }
  
  .company-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    margin-bottom: 40px;
    
    .company-card {
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      flex-direction: column;
      height: 100%;
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        
        .company-image img {
          transform: scale(1.05);
        }
      }
      
      .company-image {
        height: 200px;
        overflow: hidden;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.6s ease;
        }
      }
      
      .company-info {
        padding: 25px;
        position: relative;
        flex: 1;
        display: flex;
        flex-direction: column;
        
        .company-logo {
          position: absolute;
          top: -30px;
          left: 25px;
          width: 60px;
          height: 60px;
          border-radius: 12px;
          overflow: hidden;
          box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
          background: white;
          display: flex;
          align-items: center;
          justify-content: center;
          
          img {
            width: 80%;
            height: 80%;
            object-fit: contain;
          }
        }
        
        .company-name {
          margin: 10px 0 15px;
          font-size: 22px;
          font-weight: 700;
          color: #0f172a;
        }
        
        .company-description {
          color: #475569;
          font-size: 14px;
          line-height: 1.5;
          margin-bottom: 20px;
          flex: 1;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        
        .company-details {
          display: flex;
          justify-content: space-between;
          margin-top: auto;
          
          .company-rating {
            .el-rate {
              height: 20px;
              line-height: 20px;
            }
          }
          
          .company-established {
            font-size: 14px;
            
            .label {
              color: #64748b;
              margin-right: 5px;
            }
            
            .value {
              font-weight: 500;
              color: #1e40af;
            }
          }
        }
      }
    }
  }
  
  .view-more {
    text-align: center;
    
    .el-button {
      font-size: 16px;
      padding: 12px 30px;
      border-radius: 8px;
    }
  }
}

@media (max-width: 1200px) {
  .alumni-company-section {
    .container {
      max-width: 960px;
    }
  }
}

@media (max-width: 992px) {
  .alumni-company-section {
    padding: 50px 0;
    
    .section-header {
      margin-bottom: 30px;
      
      .section-title {
        font-size: 28px;
      }
    }
    
    .company-grid {
      gap: 20px;
    }
  }
}

@media (max-width: 768px) {
  .alumni-company-section {
    padding: 40px 0;
    
    .company-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style> 