<template>
  <div class="home">
    <!-- 顶部导航 -->
    <HeaderNav :categories="categoryTree" />
    <!-- 主要内容区域 -->
    <main class="main-content">
      <div class="container">
        <!-- 主内容区布局：产品分类侧边栏 + 轮播图 -->
        <div class="main-layout">
          <!-- 左侧产品分类 -->
          <div class="sidebar-container">
            <CategorySidebar :categories="categoryTree" />
          </div>
          
          <!-- 右侧轮播图区域 -->
          <section class="hero-section">
            <HeroBanner :banners="bannerList" />
          </section>
        </div>
      </div>
      
      <!-- 校友企业风采展示区域 -->
      <AlumniCompanySection :companies="companyList" />
    </main>
    
    <!-- 产品展示区域 -->
    <ProductSection :hot-categories="hotCategoryList" />
    
    <!-- 关于我们区域 -->
    <AboutSection />
    
    <!-- 底部区域 -->
    <Footer />
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import HeaderNav from '../components/HeaderNav.vue'
import HeroBanner from '../components/HeroBanner.vue'
import CategorySidebar from '../components/CategorySidebar.vue'
import AlumniCompanySection from '../components/AlumniCompanySection.vue'
import ProductSection from '../components/ProductSection.vue'
import AboutSection from '../components/PurchaseSection.vue'
import Footer from '../components/Footer.vue'
import { useLanguageStore } from '../stores/language.js'
import { 
  getBanners, 
  getCategories, 
  getMerchantShowcase, 
  getHotCategories 
} from '../api/home'

export default {
  name: 'HomeView',
  components: {
    HeaderNav,
    HeroBanner,
    CategorySidebar,
    AlumniCompanySection,
    ProductSection,
    AboutSection,
    Footer
  },
  setup() {
    const languageStore = useLanguageStore()
    
    const bannerList = ref([])
    const categoryTree = ref([])
    const companyList = ref([])
    const hotCategoryList = ref([])
    
    const loadHomePageData = async () => {
      try {
        console.log('开始加载首页数据...')
        const [bannerRes, categoryRes, companyRes, hotCategoryRes] = await Promise.all([
          getBanners(),
          getCategories(),
          getMerchantShowcase({ page: 1, page_size: 6 }),
          getHotCategories({ page: 1, page_size: 10 })
        ])
        
        console.log('轮播图接口响应:', bannerRes)
        if (bannerRes.code === 0) {
          bannerList.value = bannerRes.data
          console.log('轮播图数据已设置:', bannerList.value)
        } else {
          console.error('轮播图接口返回错误:', bannerRes)
        }
        
        if (categoryRes.code === 0) {
          categoryTree.value = categoryRes.data
        }
        if (companyRes.code === 0) {
          companyList.value = companyRes.data
        }
        if (hotCategoryRes) {
          hotCategoryList.value = hotCategoryRes
        }
      } catch (error) {
        console.error('Failed to load home page data:', error)
      }
    }
    
    onMounted(() => {
      languageStore.initLanguage()
      loadHomePageData()
    })
    
    return {
      bannerList,
      categoryTree,
      companyList,
      hotCategoryList
    }
  }
}
</script>

<style lang="scss" scoped>
.home {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #ffffff 50%, #f1f5f9 100%);
}

.main-content {
  padding: 32px 0 40px 0;
  padding-top: 140px; // 调整以适应新的头部高度 (主导航70px + 二级导航50px + 间距20px)
  position: relative;
  
  .container {
    max-width: 1620px !important;
    margin: 0 auto;
    padding: 0 30px !important;
    position: relative;
    overflow: visible;
  }
  
  .main-layout {
    display: flex;
    gap: 30px;
    margin-bottom: 40px;
    animation: fadeIn 0.8s ease-out;
    
    .sidebar-container {
      flex-shrink: 0;
      width: 260px;
      animation: slideInLeft 0.8s ease-out;
    }
    
    .hero-section {
      flex-grow: 1;
      width: calc(100% - 290px);
      animation: slideInRight 0.8s ease-out;
      z-index: 1;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@media (max-width: 1366px) and (min-width: 1025px) {
  .main-content {
    padding-top: 125px; // 主导航65px + 二级导航45px + 间距15px
    
    .container {
      max-width: 1200px !important;
      padding: 0 24px !important;
    }
    
    .main-layout {
      gap: 24px;
      
      .hero-section {
        width: calc(100% - 284px);
      }
    }
  }
}

@media (max-width: 1024px) and (min-width: 969px) {
  .main-content {
    padding-top: 115px; // 主导航60px + 二级导航42px + 间距13px
    
    .container {
      max-width: 1000px !important;
      padding: 0 20px !important;
    }
    
    .main-layout {
      gap: 20px;
      
      .hero-section {
        width: calc(100% - 280px);
      }
    }
  }
}

@media (max-width: 968px) {
  .main-content {
    padding: 12px 0 24px 0;
    padding-top: 90px; // 移动端头部较高
    
    .container {
      padding: 0 8px !important;
      max-width: 100% !important;
    }
    
    .main-layout {
      flex-direction: column;
      gap: 12px;
      margin-bottom: 20px;
      
      .sidebar-container,
      .hero-section {
        width: 100%;
        min-width: 0;
      }
    }
  }
  
  .home {
    font-size: 15px;
  }
  
  .alumni-company-section,
  .product-section,
  .about-section {
    padding: 12px 0 !important;
  }
  
  .footer {
    margin-top: 24px !important;
    padding: 24px 0 12px 0 !important;
    .footer-bottom-content {
      flex-direction: column !important;
      gap: 8px;
      text-align: center;
    }
    .beian-info {
      margin-top: 4px;
    }
  }
}

// 针对超小屏幕（如375px）进一步优化
@media (max-width: 480px) {
  .main-content {
    padding-top: 70px;
  }
  .home {
    font-size: 14px;
  }
  .footer {
    font-size: 13px;
    .footer-section {
      .footer-title { font-size: 15px; }
      .footer-description { font-size: 13px; }
      .footer-links li a { font-size: 13px; }
      .contact-item { font-size: 13px; }
    }
  }
}
</style>
