import axios from 'axios'

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_APP_API_BASE_URL || '/',
  timeout: 10000
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 添加语言头
    config.headers['Accept-Language'] = 'zh-CN'
    // 可以在这里添加token等认证信息
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    const res = response.data
    // 返回完整的响应数据，让调用方自己处理
    return res
  },
  error => {
    console.error('Request Error:', error.message)
    return Promise.reject(error)
  }
)

export default request
