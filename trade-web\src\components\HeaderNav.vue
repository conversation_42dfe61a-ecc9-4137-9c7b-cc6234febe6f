<template>
  <header class="header-nav" ref="headerRef">
    <!-- 主导航栏 -->
    <div class="main-nav">
      <div class="container">
        <div class="nav-content">
          <!-- Logo -->
          <div class="logo" @click="goTo('/')">
            <img src="../assets/logo.png" alt="Logo" class="logo-img" />
            <div class="logo-text">
              <h2>企业出海服务分会</h2>
              <span class="subtitle">华南理工大学广州校友会</span>
            </div>
          </div>
          
          <!-- 搜索区域 -->
          <div class="search-area">
            <div class="search-wrapper">
              <!-- 搜索输入框 -->
              <div class="search-input-wrapper">
                <el-icon class="search-icon"><Search /></el-icon>
                <input 
                  v-model="searchQuery"
                  type="text"
                  :placeholder="currentLang === 'zh' ? '搜索产品、供应商...' : 'Search products, suppliers...'"
                  class="search-input"
                  @keyup.enter="handleSearch"
                >
              </div>
              <!-- 搜索按钮 -->
              <button class="search-button" @click="handleSearch">
                <span>{{ currentLang === 'zh' ? '搜索' : 'Search' }}</span>
              </button>
            </div>
          </div>
          
          <!-- 右侧图标区 -->
          <div class="icons-area">
            <div class="icon-item">
              <el-icon><User /></el-icon>
              <span>{{ currentLang === 'zh' ? '登录' : 'Sign In' }}</span>
            </div>
            <div class="icon-item">
              <el-icon><Message /></el-icon>
              <span>{{ currentLang === 'zh' ? '消息' : 'Messages' }}</span>
            </div>
            <div class="icon-item">
              <el-icon><ShoppingCart /></el-icon>
              <span>{{ currentLang === 'zh' ? '购物车' : 'Cart' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 二级导航栏 -->
    <div class="secondary-nav">
      <div class="container">
        <div class="secondary-content">
          <!-- 产品分类 -->
          <div class="categories-area">
            <div 
              class="categories-trigger"
              @mouseenter="showCategories"
              @mouseleave="hideCategories"
            >
              <el-icon><Menu /></el-icon>
              <span>{{ currentLang === 'zh' ? '全部分类' : 'All Categories' }}</span>
              <el-icon class="arrow-icon"><ArrowDown /></el-icon>
            </div>
            
            <!-- 分类下拉面板 -->
            <transition name="slide-down">
              <div 
                v-if="showCategoriesPanel"
                class="categories-panel"
                @mouseenter="keepCategories"
                @mouseleave="hideCategories"
              >
                <div class="categories-content">
                  <div 
                    v-for="category in categories" 
                    :key="category.id"
                    class="category-item"
                    @mouseenter="showSubCategories(category)"
                    @click="selectCategory(category)"
                  >
                    <el-icon class="category-icon" v-if="category.logo">
                      <img :src="category.logo" :alt="category.name" class="icon-img"/>
                    </el-icon>
                    <span class="category-name">
                      {{ category.name }}
                    </span>
                    <el-icon v-if="category.sub_categories && category.sub_categories.length" class="arrow-icon"><ArrowRight /></el-icon>
                  </div>
                </div>
                
                <!-- 子分类面板 -->
                <transition name="slide-right">
                  <div 
                    v-if="hoveredCategory && hoveredCategory.sub_categories && hoveredCategory.sub_categories.length"
                    class="subcategories-panel"
                  >
                    <div class="subcategories-content">
                      <div 
                        v-for="subCategory in hoveredCategory.sub_categories" 
                        :key="subCategory.id"
                        class="subcategory-group"
                      >
                        <h4 class="subcategory-title" @click="selectCategory(subCategory)">
                          {{ subCategory.name }}
                        </h4>
                        <ul class="subcategory-items" v-if="subCategory.sub_categories">
                          <li 
                            v-for="item in subCategory.sub_categories" 
                            :key="item.id"
                            class="subcategory-item"
                            @click="selectCategory(item)"
                          >
                            {{ item.name }}
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </transition>
              </div>
            </transition>
          </div>
          
          <!-- 面包屑导航 -->
          <div class="breadcrumb-area">
            <div class="breadcrumb">
              <span 
                v-for="(item, index) in breadcrumbItems" 
                :key="index"
                class="breadcrumb-item"
              >
                <span 
                  v-if="item.path"
                  @click="goTo(item.path)"
                  class="breadcrumb-link"
                >
                  {{ item.name }}
                </span>
                <span v-else class="breadcrumb-text">
                  {{ item.name }}
                </span>
                <span v-if="index < breadcrumbItems.length - 1" class="breadcrumb-separator">
                  /
                </span>
              </span>
            </div>
          </div>
          
          <!-- 语言切换 -->
          <div class="language-area">
            <el-dropdown @command="switchLanguage" class="lang-dropdown">
              <span class="lang-trigger">
                {{ languages.find(l => l.code === currentLang)?.flag }}
                {{ languages.find(l => l.code === currentLang)?.name }}
                <el-icon><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item 
                    v-for="lang in languages" 
                    :key="lang.code"
                    :command="lang.code"
                    :class="{ active: currentLang === lang.code }"
                  >
                    {{ lang.flag }} {{ lang.name }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Search, User, Message, ShoppingCart, Menu, ArrowDown, ArrowRight } from '@element-plus/icons-vue'
import { languages } from '../data/mockData.js' // categoryData removed
import { useLanguageStore } from '../stores/language.js'

export default {
  name: 'HeaderNav',
  props: {
    categories: {
      type: Array,
      required: true,
      default: () => []
    }
  },
  setup(props) {
    const router = useRouter()
    const route = useRoute()
    const languageStore = useLanguageStore()
    const headerRef = ref(null)
    
    const searchQuery = ref('')
    const showCategoriesPanel = ref(false)
    const hoveredCategory = ref(null)
    let hideTimer = null
    
    const currentLang = computed(() => languageStore.currentLang)
    
    const switchLanguage = (langCode) => {
      languageStore.setLanguage(langCode)
      // Potentially reload data or let watchers handle it
    }
    
    const findCategoryById = (id, categories) => {
      for (const category of categories) {
        if (category.id === id) return category
        if (category.sub_categories) {
          const found = findCategoryById(id, category.sub_categories)
          if (found) return found
        }
      }
      return null
    }

    const breadcrumbItems = computed(() => {
      const items = [{ name: currentLang.value === 'zh' ? '首页' : 'Home', path: '/' }]
      const categoryId = parseInt(route.params.categoryId)
      if (route.name === 'category-detail' && categoryId) {
        const category = findCategoryById(categoryId, props.categories)
        if (category) {
          items.push({ name: category.name, path: null })
        }
      }
      // ... other breadcrumb logic
      return items
    })
    
    const goTo = (path) => {
      if (path) router.push(path)
    }
    
    const handleSearch = () => {
      if (searchQuery.value.trim()) {
        console.log('搜索:', searchQuery.value)
      }
    }
    
    const showCategories = () => {
      if (hideTimer) clearTimeout(hideTimer)
      showCategoriesPanel.value = true
    }
    
    const hideCategories = () => {
      hideTimer = setTimeout(() => {
        showCategoriesPanel.value = false
        hoveredCategory.value = null
      }, 200)
    }
    
    const keepCategories = () => {
      if (hideTimer) clearTimeout(hideTimer)
    }
    
    const showSubCategories = (category) => {
      hoveredCategory.value = category
    }
    
    const selectCategory = (category) => {
      if (category.category_url) {
        window.location.href = category.category_url;
      } else if (category.id) {
        router.push(`/category/${category.id}`)
      }
      showCategoriesPanel.value = false
      hoveredCategory.value = null
    }

    const updateHeaderHeight = () => {
      if (headerRef.value) {
        const height = headerRef.value.offsetHeight;
        document.documentElement.style.setProperty('--header-height', `${height}px`);
      }
    };

    onMounted(() => {
      updateHeaderHeight();
      window.addEventListener('resize', updateHeaderHeight);
    });

    onUnmounted(() => {
      window.removeEventListener('resize', updateHeaderHeight);
    });
    
    return {
      headerRef,
      currentLang,
      languages,
      searchQuery,
      showCategoriesPanel,
      hoveredCategory,
      breadcrumbItems,
      switchLanguage,
      handleSearch,
      showCategories,
      hideCategories,
      keepCategories,
      showSubCategories,
      selectCategory,
      goTo,
      Search, User, Message, ShoppingCart, Menu, ArrowDown, ArrowRight
    }
  }
}
</script>

<style lang="scss" scoped>
.header-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: white;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
}

.main-nav {
  border-bottom: 1px solid #f0f0f0;
  
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
  
  .nav-content {
    display: flex;
    align-items: center;
    height: 70px;
    gap: 200px;
  }
  .logo {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .logo-img {
    width: 40px;
    height: 40px;
  }
  
  .logo-text {
    h2 {
      color: #1e3a8a; // 蓝色主题
      margin: 0;
      font-size: 18px;
      font-weight: 700;
      line-height: 1.2;
    }
    
    .subtitle {
      font-size: 13px;
      color: #3b82f6;
      display: block;
      margin-top: 3px;
    }
  }
}
  
  .search-area {
    flex: 1;
    max-width: 750px;
    
    .search-wrapper {
      display: flex;
      background: #f8f9fa;
      border-radius: 8px;
      border: 2px solid transparent;
      border-color: #efefef;
      transition: all 0.3s ease;
      
      &:hover, &:focus-within {
        border-color: #2563eb;
        background: white;
        box-shadow: 0 4px 12px rgba(37, 99, 235, 0.1);
      }
      
      .search-input-wrapper {
        flex: 1;
        display: flex;
        align-items: center;
        padding: 0 16px;
        
        .search-icon {
          color: #6b7280;
          font-size: 16px;
          margin-right: 12px;
        }
        
        .search-input {
          flex: 1;
          border: none;
          outline: none;
          background: transparent;
          font-size: 16px;
          height: 44px;
          
          &::placeholder {
            color: #6b7280;
          }
        }
      }
      
      .search-button {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        color: white;
        border: none;
        padding: 0 24px;
        border-radius: 0 6px 6px 0;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover {
          background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
        }
      }
    }
  }
  
  .icons-area {
    display: flex;
    gap: 20px;
    
    .icon-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      cursor: pointer;
      padding: 8px;
      border-radius: 8px;
      transition: all 0.3s ease;
      
      &:hover {
        background: #f0f7ff;
        color: #2563eb;
      }
      
      .el-icon {
        font-size: 20px;
      }
      
      span {
        font-size: 12px;
        font-weight: 500;
      }
    }
  }
}

.secondary-nav {
  border-bottom: 1px solid #e5e7eb;
  
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
  
  .secondary-content {
    display: flex;
    align-items: center;
    height: 50px;
    gap: 30px;
  }
  
  .categories-area {
    position: relative;
    
    .categories-trigger {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 10px 16px;
      // background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
      color: #333;
      border-radius: 6px;
      cursor: pointer;
      font-weight: 600;
      transition: all 0.3s ease;
      
      &:hover {
        // background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
      }
      
      .arrow-icon {
        font-size: 14px;
        transition: transform 0.3s ease;
      }
    }
    
    .categories-panel {
      position: absolute;
      top: 100%;
      left: 0;
      background: white;
      border-radius: 8px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
      border: 1px solid #e5e7eb;
      z-index: 1001;
      display: flex;
      
      .categories-content {
        width: 280px;
        padding: 16px 0;
        
        .category-item {
          display: flex;
          align-items: center;
          padding: 12px 20px;
          cursor: pointer;
          transition: all 0.3s ease;
          
          &:hover {
            background: #f0f7ff;
            color: #2563eb;
          }
          
          .category-icon {
            font-size: 18px;
            margin-right: 12px;
            color: #2563eb;
          }
          
          .category-name {
            flex: 1;
            font-weight: 500;
          }
          
          .arrow-icon {
            font-size: 14px;
            color: #6b7280;
          }
        }
      }
      
      .subcategories-panel {
        width: 400px;
        border-left: 1px solid #e5e7eb;
        padding: 16px;
        background: #fafafa;
        
        .subcategories-content {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 20px;
          
          .subcategory-group {
            .subcategory-title {
              font-size: 14px;
              font-weight: 600;
              color: #333;
              margin: 0 0 8px 0;
              padding-bottom: 4px;
              border-bottom: 1px solid #2563eb;
            }
            
            .subcategory-items {
              list-style: none;
              margin: 0;
              padding: 0;
              
              .subcategory-item {
                padding: 4px 0;
                font-size: 13px;
                color: #666;
                cursor: pointer;
                transition: all 0.3s ease;
                
                &:hover {
                  color: #2563eb;
                  padding-left: 8px;
                }
              }
            }
          }
        }
      }
    }
  }
  
  .breadcrumb-area {
    flex: 1;
  }
  
  .language-area {
    .lang-dropdown {
      .lang-trigger {
        display: flex;
        align-items: center;
        gap: 6px;
        cursor: pointer;
        padding: 8px 12px;
        border-radius: 6px;
        transition: all 0.3s ease;
        font-weight: 500;
        color: #666;
        
        &:hover {
          background: #f8f9fa;
          color: #2563eb;
        }
      }
    }
  }
  
  .breadcrumb-area {
    .breadcrumb-placeholder {
      font-size: 14px;
      color: #666;
    }
  }
}

// 动画效果
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.slide-down-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.3s ease;
}

.slide-right-enter-from {
  opacity: 0;
  transform: translateX(-20px);
}

.slide-right-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

// 响应式适配
@media (max-width: 1366px) and (min-width: 1025px) {
  .main-nav .nav-content {
    height: 65px;
    gap: 200px;
  }
  
  .secondary-nav .secondary-content {
    height: 45px;
  }
}

@media (max-width: 1024px) and (min-width: 969px) {
  .main-nav .nav-content {
    height: 60px;
    gap: 150px;
  }
  
  .secondary-nav .secondary-content {
    height: 42px;
  }
}

@media (max-width: 968px) {
  .main-nav {
    .nav-content {
      flex-direction: row;
      flex-wrap: wrap;
      align-items: center;
      justify-content: space-between;
      height: auto;
      padding: 10px 0;
      gap: 10px;
    }
    .logo {
      flex: 1;
      min-width: 0;
      gap: 10px;
      .logo-img {
        width: 32px;
        height: 32px;
      }
      .logo-text {
        h2 {
          font-size: 16px;
        }
        .subtitle {
          font-size: 12px;
        }
      }
    }
    .search-area {
      width: 100%;
      max-width: none;
      order: 3;
      margin-top: 8px;
      .search-wrapper {
        .search-input-wrapper {
          .search-input {
            font-size: 15px;
            height: 40px;
          }
        }
        .search-button {
          padding: 0 16px;
          font-size: 14px;
          height: 40px;
        }
      }
    }
    .icons-area {
      flex: 0 0 auto;
      display: flex;
      gap: 8px;
      order: 2;
      .icon-item {
        padding: 4px 6px;
        .el-icon {
          font-size: 18px;
        }
        span {
          font-size: 11px;
        }
      }
    }
  }
  
  .secondary-nav {
    .secondary-content {
      flex-direction: row;
      height: auto;
      padding: 6px 0;
      gap: 0;
      justify-content: space-between;
      align-items: center;
      font-size: 15px;
    }
    .categories-area,
    .breadcrumb-area,
    .language-area {
      width: 33.33%;
      display: flex;
      justify-content: center;
      align-items: center;
      min-width: 0;
    }
    .categories-area {
      .categories-trigger {
        width: auto;
        min-width: 0;
        padding: 6px 4px;
        font-size: 15px;
      }
    }
    .breadcrumb-area {
      .breadcrumb {
        font-size: 15px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        justify-content: center;
      }
    }
    .language-area {
      .lang-trigger {
        font-size: 15px;
        padding: 6px 4px;
      }
    }
  }
}

// 全局样式：解决头部遮挡问题
@media (max-width: 968px) {
  .main-content {
    padding-top: 200px !important;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding-top: 180px !important;
  }
}

.category-icon .icon-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
</style> 