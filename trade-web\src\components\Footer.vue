<template>
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-section">
          <div class="footer-logo">
            <img src="../assets/logo.png" alt="Logo" class="logo-img" />
            <div class="logo-text">
              <h3>企业出海服务分会</h3>
              <span class="subtitle">华南理工大学广州校友会</span>
            </div>
          </div>
          <p class="footer-description">
            {{ currentLang === 'zh' ? 
              '致力于为校友企业提供国际化发展支持，打造交流合作平台，促进企业走向全球市场。' : 
              'Committed to providing internationalization support for alumni enterprises, creating a platform for exchange and cooperation, and promoting enterprises to go global.' }}
          </p>
        </div>
        
        <div class="footer-section">
          <h4 class="footer-title">{{ currentLang === 'zh' ? '快速链接' : 'Quick Links' }}</h4>
          <ul class="footer-links">
            <li><a href="/">{{ currentLang === 'zh' ? '首页' : 'Home' }}</a></li>
            <li><a href="/about">{{ currentLang === 'zh' ? '关于我们' : 'About Us' }}</a></li>
            <li><a href="#">{{ currentLang === 'zh' ? '产品中心' : 'Products' }}</a></li>
            <li><a href="#">{{ currentLang === 'zh' ? '联系我们' : 'Contact' }}</a></li>
          </ul>
        </div>
        
        <div class="footer-section">
          <h4 class="footer-title">{{ currentLang === 'zh' ? '联系方式' : 'Contact Info' }}</h4>
          <div class="contact-info">
            <div class="contact-item">
              <el-icon><Location /></el-icon>
              <span>{{ currentLang === 'zh' ? '广州市天河区华南理工大学' : 'South China University of Technology, Tianhe District, Guangzhou' }}</span>
            </div>
            <div class="contact-item">
              <el-icon><Phone /></el-icon>
              <span>020-12345678</span>
            </div>
            <div class="contact-item">
              <el-icon><Message /></el-icon>
              <span><EMAIL></span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="footer-bottom">
        <div class="footer-bottom-content">
          <div class="copyright">
            <p>&copy; {{ new Date().getFullYear() }} {{ currentLang === 'zh' ? '华南理工大学广州校友会企业出海服务分会' : 'SCUT Guangzhou Alumni Association Enterprise Overseas Service Branch' }}. {{ currentLang === 'zh' ? '保留所有权利。' : 'All rights reserved.' }}</p>
          </div>
          <div class="beian-info">
            <a 
              href="https://beian.miit.gov.cn" 
              target="_blank" 
              rel="noopener noreferrer"
              class="beian-link"
            >
              {{ currentLang === 'zh' ? '粤ICP备2025446131号-1' : 'ICP No. 2025446131-1' }}
            </a>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script>
import { computed } from 'vue'
import { useLanguageStore } from '../stores/language.js'
import { Location, Phone, Message } from '@element-plus/icons-vue'

export default {
  name: 'Footer',
  components: {
    Location,
    Phone,
    Message
  },
  setup() {
    const languageStore = useLanguageStore()
    const currentLang = computed(() => languageStore.currentLang)
    
    return {
      currentLang
    }
  }
}
</script>

<style lang="scss" scoped>
.footer {
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #3b82f6 100%);
  color: white;
  padding: 40px 0 20px 0;
  margin-top: 60px;
}

.container {
  max-width: 1620px;
  margin: 0 auto;
  padding: 0 30px;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 40px;
  margin-bottom: 30px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 30px;
  }
}

.footer-section {
  .footer-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    
    .logo-img {
      width: 40px;
      height: 40px;
    }
    
    .logo-text {
      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: white;
      }
      
      .subtitle {
        font-size: 12px;
        color: #bfdbfe;
        display: block;
        margin-top: 2px;
      }
    }
  }
  
  .footer-description {
    color: #bfdbfe;
    line-height: 1.6;
    margin: 0;
    font-size: 14px;
  }
  
  .footer-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 16px 0;
    color: white;
  }
  
  .footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
    
    li {
      margin-bottom: 8px;
      
      a {
        color: #bfdbfe;
        text-decoration: none;
        transition: color 0.3s ease;
        font-size: 14px;
        
        &:hover {
          color: white;
        }
      }
    }
  }
  
  .contact-info {
    .contact-item {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      color: #bfdbfe;
      font-size: 14px;
      
      .el-icon {
        font-size: 16px;
        color: #93c5fd;
      }
    }
  }
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 20px;
  
  .footer-bottom-content {
    display: flex;
    align-items: center;
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 12px;
      text-align: center;
    }
  }
  
  .copyright {
    p {
      margin: 0;
      color: #bfdbfe;
      font-size: 14px;
    }
  }
  
  .beian-info {
    .beian-link {
      color: #bfdbfe;
      text-decoration: none;
      font-size: 14px;
      transition: color 0.3s ease;
      
      &:hover {
        color: white;
        text-decoration: underline;
      }
    }
  }
}

@media (max-width: 1366px) {
  .container {
    max-width: 1200px;
    padding: 0 24px;
  }
  
  .footer-content {
    gap: 30px;
  }
}

@media (max-width: 1024px) {
  .container {
    max-width: 1000px;
    padding: 0 20px;
  }
  
  .footer-content {
    gap: 25px;
  }
}
</style> 