<template>
  <div class="product-detail">
    <!-- 固定头部 -->
    <HeaderNav />
    
    <!-- 主要内容区域 -->
    <main class="main-content">
      <div class="container">
        <!-- 面包屑导航 -->
        <!-- <div class="breadcrumb">
          <span @click="$router.push('/')" class="breadcrumb-item">
            {{ currentLang === 'zh' ? '首页' : 'Home' }}
          </span>
          <span class="separator">/</span>
          <span v-if="currentCategory" @click="goToCategory" class="breadcrumb-item">
            {{ currentLang === 'zh' ? currentCategory.name : currentCategory.nameEn }}
          </span>
          <span class="separator">/</span>
          <span v-if="product" class="breadcrumb-item active">
            {{ currentLang === 'zh' ? product.name : product.nameEn }}
          </span>
        </div> -->
        
        <!-- 产品主要信息 -->
        <div v-if="product" class="product-main">
          <div class="product-layout">
            <!-- 左侧：产品图片 -->
            <div class="product-images">
              <ProductImageGallery 
                :images="product.images || [product.image]"
                :product-name="currentLang === 'zh' ? product.name : product.nameEn"
                class="product-gallery"
              />
            </div>
            
            <!-- 右侧：产品信息 -->
            <div class="product-info">
              <h1 class="product-title">
                {{ currentLang === 'zh' ? product.name : product.nameEn }}
              </h1>
              
              <div class="price-section">
                <span class="price-label">{{ currentLang === 'zh' ? '价格：' : 'Price:' }}</span>
                <span class="price">{{ product.price }}</span>
              </div>
<!--               
              <div class="product-meta">
                <div class="meta-item">
                  <span class="meta-label">{{ currentLang === 'zh' ? '起购量：' : 'Min Order:' }}</span>
                  <span class="meta-value">{{ product.minOrder }} {{ currentLang === 'zh' ? product.minOrderUnit : product.minOrderUnitEn }}</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">{{ currentLang === 'zh' ? '库存：' : 'Stock:' }}</span>
                  <span class="meta-value">{{ product.stock }}</span>
                </div>
              </div> -->
              
              <div class="product-summary">
                <h3>{{ currentLang === 'zh' ? '产品简介' : 'Product Summary' }}</h3>
                <p>{{ currentLang === 'zh' ? product.description : product.descriptionEn }}</p>
              </div>
              
              <div v-if="product.specifications" class="specifications">
                <h3>{{ currentLang === 'zh' ? '规格参数' : 'Specifications' }}</h3>
                <div class="spec-grid">
                  <div v-for="(value, key) in product.specifications" :key="key" class="spec-item">
                    <span class="spec-key">{{ key }}:</span>
                    <span class="spec-value">{{ value }}</span>
                  </div>
                </div>
              </div>
              
              <!-- 供应商信息 -->
              <div v-if="company" class="supplier-info">
                <h3>{{ currentLang === 'zh' ? '供应商信息' : 'Supplier Information' }}</h3>
                <div class="supplier-card">
                  <div class="supplier-header">
                    <img :src="company.avatar" :alt="company.name" class="supplier-avatar" @click="viewCompanyProfile" style="cursor: pointer;" />
                    <div class="supplier-basic">
                      <h4 class="supplier-name" @click="viewCompanyProfile" style="cursor: pointer;">
                        {{ currentLang === 'zh' ? company.name : company.nameEn }}
                      </h4>
                      <div class="supplier-rating">
                        <div class="stars">
                          <span v-for="i in 5" :key="i" class="star" :class="{ filled: i <= Math.floor(company.rating) }">★</span>
                          <span class="rating-number">{{ company.rating }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- <div class="supplier-details">
                    <div class="detail-row">
                      <el-icon><LocationFilled /></el-icon>
                      <span>{{ currentLang === 'zh' ? company.address : company.addressEn }}</span>
                    </div>
                    <div class="detail-row">
                      <el-icon><OfficeBuilding /></el-icon>
                      <span>{{ currentLang === 'zh' ? company.businessType : company.businessTypeEn }}</span>
                    </div>
                  </div> -->
                  
                  <!-- 添加查看公司主页按钮 -->
                  <div class="view-company-btn-wrapper">
                    <el-button type="primary" plain size="small" @click="viewCompanyProfile">
                      <el-icon><Office /></el-icon>
                      {{ currentLang === 'zh' ? '查看公司主页' : 'View Company Profile' }}
                    </el-button>
                  </div>
                </div>
              </div>
              
              <!-- 操作按钮 -->
              <div class="action-buttons">
                <el-button type="primary" size="large" @click="contactSupplier" class="contact-btn">
                  <el-icon><Phone /></el-icon>
                  {{ currentLang === 'zh' ? '立即联系' : 'Contact Now' }}
                </el-button>
                <el-button size="large" @click="addToInquiry" class="inquiry-btn">
                  <el-icon><ShoppingCart /></el-icon>
                  {{ currentLang === 'zh' ? '询价单' : 'Inquiry Cart' }}
                </el-button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 详细信息标签 -->
        <div v-if="product" class="detail-tabs">
          <div class="tabs-header">
            <div 
              class="tab-item"
              :class="{ active: activeTab === 'description' }"
              @click="activeTab = 'description'"
            >
              {{ currentLang === 'zh' ? '产品描述' : 'Product Description' }}
            </div>
            <div 
              class="tab-item"
              :class="{ active: activeTab === 'company' }"
              @click="activeTab = 'company'"
            >
              {{ currentLang === 'zh' ? '公司信息' : 'Company Information' }}
            </div>
          </div>
          
          <div class="tabs-content">
            <!-- 产品描述标签 -->
            <div v-if="activeTab === 'description'" class="tab-panel">
              <div class="rich-content" v-html="currentLang === 'zh' ? product.detailDescription : product.detailDescriptionEn"></div>
            </div>
            
            <!-- 公司信息标签 -->
            <div v-if="activeTab === 'company' && company" class="tab-panel">
              <div class="company-images" v-if="company.companyImages">
                <div class="company-gallery">
                  <img 
                    v-for="(image, index) in company.companyImages" 
                    :key="index"
                    :src="image" 
                    :alt="`${company.name} ${index + 1}`"
                    class="company-image"
                  />
                </div>
              </div>
              
              <div class="rich-content" v-html="currentLang === 'zh' ? company.companyDescription : company.companyDescriptionEn"></div>
              
              <div v-if="company.mainMarkets" class="main-markets">
                <h3>{{ currentLang === 'zh' ? '主要市场' : 'Main Markets' }}</h3>
                <div class="markets-list">
                  <span 
                    v-for="market in (currentLang === 'zh' ? company.mainMarkets : company.mainMarketsEn)" 
                    :key="market"
                    class="market-tag"
                  >
                    {{ market }}
                  </span>
                </div>
              </div>
              
              <!-- 联系我们表单区域 -->
              <div class="contact-form-section">
                <h3>{{ currentLang === 'zh' ? '联系我们' : 'Contact Us' }}</h3>
                <div class="contact-info">
                  <div class="contact-item">
                    <el-icon><Phone /></el-icon>
                    <span>{{ company.phone }}</span>
                  </div>
                  <div class="contact-item">
                    <el-icon><Message /></el-icon>
                    <span>{{ company.email }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-state">
          <el-icon class="is-loading"><Loading /></el-icon>
          <p>{{ currentLang === 'zh' ? '加载中...' : 'Loading...' }}</p>
        </div>
        
        <!-- 产品未找到 -->
        <div v-if="!loading && !product" class="not-found">
          <el-icon><DocumentDelete /></el-icon>
          <h2>{{ currentLang === 'zh' ? '产品未找到' : 'Product Not Found' }}</h2>
          <p>{{ currentLang === 'zh' ? '抱歉，您访问的产品不存在。' : 'Sorry, the product you are looking for does not exist.' }}</p>
          <el-button type="primary" @click="$router.push('/')">
            {{ currentLang === 'zh' ? '返回首页' : 'Back to Home' }}
          </el-button>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { LocationFilled, OfficeBuilding, Phone, ShoppingCart, Message, Loading, DocumentDelete } from '@element-plus/icons-vue'
import HeaderNav from '../components/HeaderNav.vue'
import ProductImageGallery from '../components/ProductImageGallery.vue'
import { categoryData, detailedProducts, companies } from '../data/mockData.js'
import { useLanguageStore } from '../stores/language.js'

export default {
  name: 'ProductDetailView',
  components: {
    HeaderNav,
    ProductImageGallery
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const languageStore = useLanguageStore()
    
    const loading = ref(true)
    const activeTab = ref('description')
    
    const currentLang = computed(() => languageStore.currentLang)
    
    // 根据路由参数获取产品
    const product = computed(() => {
      const productId = parseInt(route.params.productId)
      return detailedProducts.find(p => p.id === productId) || null
    })
    
    // 获取产品对应的公司信息
    const company = computed(() => {
      if (!product.value) return null
      return companies.find(c => c.id === product.value.companyId) || null
    })
    
    // 根据产品分类ID查找分类信息
    const findCategoryById = (categoryId) => {
      const findInCategory = (categories) => {
        for (const category of categories) {
          if (category.id === categoryId) return category
          if (category.children) {
            const found = findInCategory(category.children)
            if (found) return found
          }
        }
        return null
      }
      return findInCategory(categoryData)
    }
    
    const currentCategory = computed(() => {
      if (!product.value) return null
      return findCategoryById(product.value.categoryId)
    })
    
    // 跳转到分类页面
    const goToCategory = () => {
      if (currentCategory.value) {
        router.push(`/category/${currentCategory.value.id}`)
      }
    }
    
    // 联系供应商
    const contactSupplier = () => {
      if (company.value) {
        router.push(`/contact-supplier/${company.value.id}`)
      } else {
        ElMessage.warning(currentLang.value === 'zh' ? '供应商信息不可用' : 'Supplier information is not available')
      }
    }
    
    // 添加到询价单
    const addToInquiry = () => {
      console.log('添加到询价单')
      // 这里可以添加询价单逻辑
    }
    
    // 跳转到公司页面
    const viewCompanyProfile = () => {
      if (company.value) {
        router.push(`/company/${company.value.id}`)
      }
    }
    
    // 初始化
    onMounted(() => {
      languageStore.initLanguage()
      // 模拟加载延迟
      setTimeout(() => {
        loading.value = false
      }, 800)
    })
    
    // 监听路由变化
    watch(() => route.params.productId, () => {
      loading.value = true
      setTimeout(() => {
        loading.value = false
      }, 500)
    })
    
    return {
      loading,
      activeTab,
      currentLang,
      product,
      company,
      currentCategory,
      goToCategory,
      contactSupplier,
      addToInquiry,
      viewCompanyProfile,
      LocationFilled,
      OfficeBuilding,
      Phone,
      ShoppingCart,
      Loading,
      DocumentDelete
    }
  }
}
</script>

<style lang="scss" scoped>
.product-detail {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #ffffff 50%, #f1f5f9 100%);
}

.main-content {
  margin-top: 30px;
  padding-top: 100px;
  padding-bottom: 30px;
  
  .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
  }
}

.breadcrumb {
  margin-bottom: 20px;
  font-size: 14px;
  
  .breadcrumb-item {
    color: #6b7280;
    text-decoration: none;
    cursor: pointer;
    
    &:hover {
      color: #2563eb;
    }
    
    &.active {
      color: #1f2937;
      font-weight: 500;
      cursor: default;
    }
  }
  
  .separator {
    margin: 0 8px;
    color: #d1d5db;
  }
}

.product-main {
  margin-bottom: 30px;
  
  .product-layout {
    display: grid;
    grid-template-columns: 0.9fr 1.1fr;
    gap: 30px;
    align-items: flex-start;
  }
}

.product-images {
  position: sticky;
  top: 120px;
  height: auto;
  max-height: calc(100vh - 180px);
  overflow: visible;
  display: flex;
  flex-direction: column;
}

.product-info {
  .product-title {
    font-size: 24px;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 16px 0;
    line-height: 1.2;
  }
  
  .price-section {
    margin-bottom: 16px;
    padding: 14px;
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    border-radius: 10px;
    border: 1px solid #bfdbfe;
    
    .price-label {
      font-size: 15px;
      color: #1e40af;
      font-weight: 500;
      margin-right: 10px;
    }
    
    .price {
      font-size: 22px;
      font-weight: 700;
      color: #1e40af;
    }
  }
  
  .product-meta {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 18px;
    
    .meta-item {
      padding: 12px;
      background: white;
      border-radius: 8px;
      border: 1px solid #e5e7eb;
      
      .meta-label {
        display: block;
        font-size: 13px;
        color: #6b7280;
        margin-bottom: 2px;
      }
      
      .meta-value {
        font-size: 15px;
        font-weight: 600;
        color: #1f2937;
      }
    }
  }
  
  .product-summary {
    margin-bottom: 16px;
    
    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
      margin: 0 0 6px 0;
    }
    
    p {
      color: #4b5563;
      line-height: 1.4;
      margin: 0;
      font-size: 14px;
    }
  }
  
  .specifications {
    margin-bottom: 16px;
    
    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
      margin: 0 0 6px 0;
    }
    
    .spec-grid {
      display: grid;
      gap: 6px;
      
      .spec-item {
        display: flex;
        justify-content: space-between;
        padding: 8px 10px;
        background: #f9fafb;
        border-radius: 6px;
        
        .spec-key {
          font-weight: 500;
          color: #374151;
          font-size: 14px;
        }
        
        .spec-value {
          color: #2563eb;
          font-weight: 600;
          font-size: 14px;
        }
      }
    }
  }
  
  .supplier-info {
    margin-bottom: 16px;
    
    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
      margin: 0 0 6px 0;
    }
    
    .supplier-card {
      background: white;
      border-radius: 10px;
      padding: 12px;
      border: 1px solid #e5e7eb;
      
      .supplier-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 10px;
        
        .supplier-avatar {
          width: 48px;
          height: 48px;
          border-radius: 10px;
          object-fit: cover;
          transition: all 0.3s ease;
          
          &:hover {
            opacity: 0.8;
            transform: scale(1.05);
          }
        }
        
        .supplier-basic {
          flex: 1;
          
          .supplier-name {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 6px 0;
            transition: all 0.3s ease;
            
            &:hover {
              color: #2563eb;
            }
          }
          
          .supplier-rating .stars {
            display: flex;
            align-items: center;
            gap: 2px;
            
            .star {
              color: #d1d5db;
              font-size: 14px;
              
              &.filled {
                color: #fbbf24;
              }
            }
            
            .rating-number {
              margin-left: 6px;
              font-size: 13px;
              color: #6b7280;
              font-weight: 500;
            }
          }
        }
      }
      
      .supplier-details {
        .detail-row {
          display: flex;
          align-items: center;
          gap: 10px;
          margin-bottom: 6px;
          font-size: 13px;
          color: #6b7280;
          
          .el-icon {
            color: #9ca3af;
            font-size: 14px;
          }
        }
      }
      
      .view-company-btn-wrapper {
        margin-top: 8px;
      }
    }
    
  }
  
  .action-buttons {
    display: flex;
    gap: 12px;
    
    .contact-btn {
      flex: 1;
      height: 42px;
      font-size: 15px;
      font-weight: 600;
      background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
      border: none;
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
      }
    }
    
    .inquiry-btn {
      flex: 1;
      height: 42px;
      font-size: 15px;
      font-weight: 600;
      border: 2px solid #2563eb;
      color: #2563eb;
      
      &:hover {
        background: #2563eb;
        color: white;
      }
    }
  }
}

.detail-tabs {
  .tabs-header {
    display: flex;
    border-bottom: 2px solid #e5e7eb;
    margin-bottom: 20px; // 减少底部间距
    
    .tab-item {
      padding: 10px 20px; // 减少内边距
      font-size: 15px;
      font-weight: 600;
      color: #6b7280;
      cursor: pointer;
      border-bottom: 3px solid transparent;
      transition: all 0.3s ease;
      
      &:hover {
        color: #2563eb;
      }
      
      &.active {
        color: #2563eb;
        border-bottom-color: #2563eb;
      }
    }
  }
  
  .tabs-content {
    .tab-panel {
      .rich-content {
        background: white;
        border-radius: 10px;
        padding: 20px; // 减少内边距
        border: 1px solid #e5e7eb;
        line-height: 1.5; // 减小行高
        
        :deep(h3) {
          color: #1f2937;
          font-size: 17px; // 减小字体
          font-weight: 600;
          margin: 0 0 10px 0; // 减少底部间距
        }
        
        :deep(p) {
          color: #4b5563;
          margin: 0 0 10px 0; // 减少底部间距
          font-size: 14px;
        }
        
        :deep(ul) {
          margin: 0 0 10px 0; // 减少底部间距
          padding-left: 18px; // 减少左侧缩进
          
          li {
            color: #4b5563;
            margin-bottom: 5px; // 减少底部间距
            font-size: 14px;
          }
        }
      }
      
      .company-images {
        margin-bottom: 20px; // 减少底部间距
        
        .company-gallery {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); // 调整列宽
          gap: 10px; // 减少间距
          
          .company-image {
            width: 100%;
            height: 160px; // 减少高度
            object-fit: cover;
            border-radius: 8px; // 减小圆角
            border: 1px solid #e5e7eb;
          }
        }
      }
      
      .main-markets {
        background: white;
        border-radius: 10px; // 减小圆角
        padding: 20px; // 减少内边距
        border: 1px solid #e5e7eb;
        margin-bottom: 20px; // 减少底部间距
        
        h3 {
          font-size: 17px; // 减小字体
          font-weight: 600;
          color: #1f2937;
          margin: 0 0 12px 0; // 减少底部间距
        }
        
        .markets-list {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          
          .market-tag {
            background: #eff6ff;
            color: #1e40af;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
          }
        }
      }
      
      .contact-form-section {
        background: white;
        border-radius: 10px; // 减小圆角
        padding: 20px; // 减少内边距
        border: 1px solid #e5e7eb;
        
        h3 {
          font-size: 17px; // 减小字体
          font-weight: 600;
          color: #1f2937;
          margin: 0 0 12px 0; // 减少底部间距
        }
        
        .contact-info {
          display: grid;
          gap: 12px;
          
          .contact-item {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 16px;
            color: #1f2937;
            
            .el-icon {
              color: #2563eb;
              font-size: 18px;
            }
          }
        }
      }
    }
  }
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  
  .el-icon {
    font-size: 48px;
    color: #6b7280;
    margin-bottom: 16px;
  }
  
  p {
    font-size: 16px;
    color: #6b7280;
    margin: 0;
  }
}

.not-found {
  text-align: center;
  padding: 80px 20px;
  
  .el-icon {
    font-size: 64px;
    color: #d1d5db;
    margin-bottom: 24px;
  }
  
  h2 {
    font-size: 24px;
    color: #1f2937;
    margin: 0 0 16px 0;
  }
  
  p {
    font-size: 16px;
    color: #6b7280;
    margin: 0 0 32px 0;
  }
}

// 响应式适配
@media (max-width: 1366px) and (min-width: 1025px) {
  .main-content {
    padding-top: 125px; // 主导航65px + 二级导航45px + 间距15px
    
    .container {
      max-width: 1200px;
      padding: 0 24px;
    }
  }
  
  .product-images {
    top: 145px;
    max-height: calc(100vh - 200px);
    overflow: visible;
  }
  
  .product-main .product-layout {
    gap: 24px;
  }
  
  .product-info {
    .product-title {
      font-size: 22px;
      margin-bottom: 14px;
    }
    
    .price-section {
      padding: 12px;
      
      .price {
        font-size: 20px;
      }
    }
    
    .action-buttons {
      .contact-btn, .inquiry-btn {
        height: 44px;
        font-size: 15px;
      }
    }
  }
  
  .detail-tabs {
    .tabs-header .tab-item {
      padding: 14px 24px;
      font-size: 15px;
    }
    
    .tabs-content .tab-panel .rich-content {
      padding: 24px;
    }
  }
}

@media (max-width: 1024px) and (min-width: 969px) {
  .main-content {
    padding-top: 115px; // 主导航60px + 二级导航42px + 间距13px
    
    .container {
      padding: 0 20px;
    }
  }
  
  .product-images {
    top: 135px;
    max-height: calc(100vh - 190px);
    overflow: visible;
  }
  
  .product-main .product-layout {
    gap: 20px;
  }
  
  .product-info {
    .product-title {
      font-size: 20px;
      margin-bottom: 12px;
    }
    
    .price-section {
      padding: 10px;
      
      .price {
        font-size: 18px;
      }
    }
    
    .product-meta {
      gap: 12px;
      
      .meta-item {
        padding: 12px;
      }
    }
    
    .action-buttons {
      gap: 12px;
      
      .contact-btn, .inquiry-btn {
        height: 42px;
        font-size: 14px;
      }
    }
  }
  
  .detail-tabs {
    .tabs-header .tab-item {
      padding: 12px 20px;
      font-size: 14px;
    }
    
    .tabs-content .tab-panel {
      .rich-content {
        padding: 20px;
      }
      
      .company-images .company-gallery {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 12px;
        
        .company-image {
          height: 160px;
        }
      }
    }
  }
}

@media (max-width: 968px) {
  .main-content {
    padding-top: 180px; // 移动端头部较高
    
    .container {
      padding: 0 20px;
    }
  }
  
  .product-main .product-layout {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .product-images {
    position: static;
    height: auto;
    max-height: none;
    margin-bottom: 20px;
    overflow: visible;
  }
  
  .product-info {
    .product-title {
      font-size: 24px;
    }
    
    .product-meta {
      grid-template-columns: 1fr;
      gap: 12px;
    }
    
    .action-buttons {
      flex-direction: column;
    }
  }
  
  .detail-tabs {
    .tabs-header .tab-item {
      padding: 12px 16px;
      font-size: 14px;
    }
    
    .tabs-content .tab-panel {
      .rich-content {
        padding: 20px;
      }
      
      .company-images .company-gallery {
        grid-template-columns: 1fr;
      }
    }
  }
}
</style>

